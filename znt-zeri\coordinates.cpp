#include "coordinates.h"

#include "config.h"
#include "main.h"

namespace zeri {

    // === Jump Spots Data ===

    JumpSpot jump_spots[] = {
        // Blue Side Base Botlane Spot
        {Position(1076.0f, 386.0f, 0.0f), Position(4154.180176f, 536.641724f, 0.0f)},
        // Blue Side Base Botlane Spot Back
        {Position(4122.0f, 534.0f, 0.0f), Position(699.264221f, 366.392456f, 0.0f)},

        // Blue Side Middle Botlane Spot
        {Position(4770.0f, 652.0f, 0.0f), Position(7916.968750f, 736.316650f, 0.0f)},
        // Blue Side Middle Botlane Spot Back
        {Position(7818.0f, 734.0f, 0.0f), Position(3834.691650f, 617.059082f, 0.0f)},

        // Blue Side End Botlane Spot
        {Position(8622.0f, 758.0f, 0.0f), Position(10624.221680f, 786.889954f, 0.0f)},
        // Blue Side End Botlane Spot Back
        {Position(10420.0f, 784.0f, 0.0f), Position(8630.421875f, 734.583557f, 0.0f)},

        // Red Side Middle Botlane Spot
        {Position(14222.0f, 10406.0f, 0.0f), Position(14145.847656f, 7278.916992f, 0.0f)},
        // Red Side Middle Botlane Spot Back
        {Position(14146.0f, 7358.0f, 0.0f), Position(14246.284180f, 10357.824219f, 0.0f)},

        // Red Side Base Botlane Spot
        {Position(14422.0f, 13856.0f, 0.0f), Position(14299.980469f, 10837.185547f, 0.0f)},
        // Red Side Base Botlane Spot Back
        {Position(14298.0f, 10806.0f, 0.0f), Position(14454.028320f, 13974.250000f, 0.0f)},

        // Blue Side Botlane Turret
        {Position(10434.0f, 788.0f, 0.0f), Position(13346.367188f, 1049.298584f, 0.0f)},
        // Blue Side Botlane Turret Back
        {Position(13172.0f, 1058.0f, 0.0f), Position(10442.958984f, 788.741516f, 0.0f)},

        // Red Side Botlane Turret
        {Position(13872.0f, 1858.0f, 0.0f), Position(14151.086914f, 4436.017578f, 0.0f)},
        // Red Side Botlane Turret Back
        {Position(14144.0f, 4394.0f, 0.0f), Position(13912.918945f, 1815.269897f, 0.0f)},

        // Blue Side top 2 spot
        {Position(624.0f, 4508.0f, 0.0f), Position(698.214661f, 7486.502930f, 0.0f)},
        // Blue Side top 2 spot Back
        {Position(700.0f, 7556.0f, 0.0f), Position(610.302490f, 4767.133789f, 0.0f)},

        // Red Side top secret:
        {Position(8022.0f, 14156.0f, 0.0f), Position(6521.248535f, 14144.303711f, 0.0f)},
        // Red Side top secret Back:
        {Position(5022.0f, 14132.0f, 0.0f), Position(7369.276367f, 14169.001953f, 0.0f)},

        // Red Side top 2spot:
        {Position(10560.0f, 14380.0f, 0.0f), Position(7697.801758f, 14154.453125f, 0.0f)},
        // Red Side top 2spot Back:
        {Position(8016.0f, 14178.0f, 0.0f), Position(10644.410156f, 14387.852539f, 0.0f)},

        // Blue Side secret:
        {Position(724.0f, 9056.0f, 0.0f), Position(745.914673f, 10564.577148f, 0.0f)},
        // Blue Side secret Back:
        {Position(750.0f, 10804.0f, 0.0f), Position(714.461670f, 9765.229492f, 0.0f)},

        // Red Side base:
        {Position(14068.0f, 14724.0f, 0.0f), Position(10214.231445f, 14288.757812f, 0.0f)},
        // Red Side base Back:
        {Position(11036.0f, 14380.0f, 0.0f), Position(14075.943359f, 14731.872070f, 0.0f)},

        // Blue Side top turret:
        {Position(774.0f, 13106.0f, 0.0f), Position(748.343506f, 10663.696289f, 0.0f)},
        // Blue Side top turret back:
        {Position(750.0f, 10804.0f, 0.0f), Position(751.187988f, 13021.580078f, 0.0f)},

        // Red Side top turret:
        {Position(1374.0f, 13906.0f, 0.0f), Position(4174.396484f, 14099.235352f, 0.0f)},
        // Red Side top turret Back:
        {Position(3918.0f, 14080.0f, 0.0f), Position(1449.433838f, 13935.067373f, 0.0f)},

        // Blue Side base:
        {Position(150.0f, 760.0f, 0.0f), Position(485.211975f, 3663.598633f, 0.0f)},
        // Blue Side base Back:
        {Position(502.0f, 3790.0f, 0.0f), Position(143.097961f, 699.289917f, 0.0f)},

        // Blue Side mid:
        {Position(7298.0f, 6644.0f, 0.0f), Position(8496.653320f, 5781.787598f, 0.0f)},
        // Blue Side mid Back:
        {Position(8550.0f, 5706.0f, 0.0f), Position(7633.916992f, 6412.936035f, 0.0f)},

        // Red Side mid:
        {Position(7562.0f, 8102.0f, 0.0f), Position(6489.558105f, 8948.484375f, 0.0f)},
        // Red Side mid Back:
        {Position(6306.0f, 9092.0f, 0.0f), Position(7809.275391f, 7921.161133f, 0.0f)},
    };

    // === Constants ===
    constexpr i32 JUMP_SPOTS_COUNT = sizeof(jump_spots) / sizeof(JumpSpot);

    // === Jump Spot Management ===

    constexpr i32 get_jump_spots_count() {
        return JUMP_SPOTS_COUNT;
    }

    const JumpSpot* find_nearest_jump_spot(const Position& pos, f32 max_distance) {
        const JumpSpot* nearest      = nullptr;
        f32             min_distance = max_distance;

        for (i32 i = 0; i < JUMP_SPOTS_COUNT; ++i) {
            f32 distance = pos.distance(jump_spots[i].from);
            if (distance < min_distance) {
                min_distance = distance;
                nearest      = &jump_spots[i];
            }
        }

        return nearest;
    }

}  // namespace zeri
