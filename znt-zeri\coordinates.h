#pragma once

#include "sdk.h"

namespace zeri {
    // Forward declarations
    class Config;
    extern Config*       config;
    extern AIHeroClient* player;

    // === Data Structures ===

    /**
     * @brief Represents a jump spot with from and to positions
     */
    struct JumpSpot {
        Position from;  ///< Starting position for the jump
        Position to;    ///< Destination position for the jump
    };

    // === Jump Spot Management ===

    /**
     * @brief Gets the array of all available jump spots
     * @return Reference to the jump spots array
     */
    extern JumpSpot jump_spots[];

    /**
     * @brief Gets the number of jump spots available
     * @return Number of jump spots in the array
     */
    constexpr i32 get_jump_spots_count();

    /**
     * @brief Finds the nearest jump spot to a given position
     * @param pos The position to search from
     * @param max_distance Maximum distance to consider
     * @return Pointer to the nearest jump spot, or nullptr if none found
     */
    const JumpSpot* find_nearest_jump_spot(const Position& pos, f32 max_distance = 400.0f);

}  // namespace zeri