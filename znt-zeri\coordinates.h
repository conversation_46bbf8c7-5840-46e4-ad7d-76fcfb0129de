#pragma once

#include "sdk.h"

namespace zeri {
    // Forward declarations
    class Config;
    extern Config*       config;
    extern AIHeroClient* player;

    // === Data Structures ===

    /**
     * @brief Represents a jump spot with from and to positions
     */
    struct JumpSpot {
        Position from;  ///< Starting position for the jump
        Position to;    ///< Destination position for the jump
    };

    // === Jump Spot Management ===

    /**
     * @brief Gets the array of all available jump spots
     * @return Reference to the jump spots array
     */
    extern JumpSpot jump_spots[];

    /**
     * @brief Gets the number of jump spots available
     * @return Number of jump spots in the array
     */
    constexpr i32 get_jump_spots_count();

    /**
     * @brief Finds the nearest jump spot to a given position
     * @param pos The position to search from
     * @param max_distance Maximum distance to consider
     * @return Pointer to the nearest jump spot, or nullptr if none found
     */
    const JumpSpot* find_nearest_jump_spot(const Position& pos, f32 max_distance = 400.0f);

    // === Position Utility Functions ===

    /**
     * @brief Checks if a position is under an enemy turret
     * @param pos The position to check
     * @param turret_range The range to consider for turret detection (default: 1000.0f)
     * @return True if position is under enemy turret range
     */
    bool is_pos_under_turret(const Position& pos, f32 turret_range = 1000.0f);

    /**
     * @brief Calculates safe tumble/dash positions around a target position
     * @param center_pos The center position to calculate around
     * @param result_pos Reference to store the best safe position
     * @param harass_mode Whether to use harass mode safety distances
     * @return True if a safe position was found
     */
    bool get_tumble_positions(const Position& center_pos, Position& result_pos, bool harass_mode = false);

    // === Position Validation Functions ===

    /**
     * @brief Validates if a position is safe from enemies
     * @param pos The position to validate
     * @param min_distance Minimum safe distance from enemies
     * @return True if position is safe
     */
    bool is_position_safe_from_enemies(const Position& pos, f32 min_distance);

    /**
     * @brief Checks if a position is within the map bounds
     * @param pos The position to check
     * @return True if position is within valid map bounds
     */
    bool is_position_in_bounds(const Position& pos);

    /**
     * @brief Validates if a position is reachable with E ability
     * @param pos The position to check
     * @return True if position is within E range
     */
    bool is_position_reachable_with_e(const Position& pos);

    // === Geometric Helper Functions ===

    /**
     * @brief Calculates positions in a circle around a center point
     * @param center The center position
     * @param radius The radius of the circle
     * @param num_points Number of points to generate
     * @return Vector of positions around the circle
     */
    Vec<Position> calculate_circle_positions(const Position& center, f32 radius, i32 num_points);

    /**
     * @brief Normalizes a direction vector
     * @param direction The direction vector to normalize
     * @return Normalized direction vector
     */
    Position normalize_direction(const Position& direction);

}  // namespace zeri