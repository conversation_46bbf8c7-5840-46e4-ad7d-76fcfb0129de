#pragma once

#include "sdk.h"

namespace zeri {
    // Forward declarations
    class Config;
    extern Config* config;
    extern AIHeroClient* player;

    // === Position Utility Functions ===

    /**
     * @brief Checks if a position is under an enemy turret
     * @param pos The position to check
     * @param turret_range The range to consider for turret detection (default: 1000.0f)
     * @return True if position is under enemy turret range
     */
    bool is_pos_under_turret(const Position& pos, f32 turret_range = 1000.0f);

    /**
     * @brief Calculates safe surrounding positions around the player for movement/dashing
     * @param center_pos The center position to calculate around
     * @param result_pos Reference to store the best safe position
     * @param harass_mode Whether to use harass mode safety distances
     * @return True if a safe position was found
     */
    bool get_surrounding_positions(const Position& center_pos, Position& result_pos, bool harass_mode = false);

    // === Position Validation Functions ===

    /**
     * @brief Validates if a position is safe from enemies
     * @param pos The position to validate
     * @param min_distance Minimum safe distance from enemies
     * @return True if position is safe
     */
    bool is_position_safe_from_enemies(const Position& pos, f32 min_distance);

    /**
     * @brief Checks if a position is within the map bounds
     * @param pos The position to check
     * @return True if position is within valid map bounds
     */
    bool is_position_in_bounds(const Position& pos);

    /**
     * @brief Validates if a position is reachable with E ability
     * @param pos The position to check
     * @return True if position is within E range
     */
    bool is_position_reachable_with_e(const Position& pos);

    /**
     * @brief Validates if a position is reachable with any ability
     * @param pos The position to check
     * @param range The range to check against
     * @return True if position is within the specified range
     */
    bool is_position_reachable(const Position& pos, f32 range);

    /**
     * @brief Checks if a position is safe for casting abilities
     * @param pos The position to check
     * @param ability_range Range of the ability being cast
     * @return True if position is safe for casting
     */
    bool is_position_safe_for_casting(const Position& pos, f32 ability_range);

    // === Geometric Helper Functions ===

    /**
     * @brief Calculates positions in a circle around a center point
     * @param center The center position
     * @param radius The radius of the circle
     * @param num_points Number of points to generate
     * @return Vector of positions around the circle
     */
    Vec<Position> calculate_circle_positions(const Position& center, f32 radius, i32 num_points);

    /**
     * @brief Calculates positions in a ring (hollow circle) around a center point
     * @param center The center position
     * @param inner_radius The inner radius of the ring
     * @param outer_radius The outer radius of the ring
     * @param num_points Number of points to generate
     * @return Vector of positions in the ring
     */
    Vec<Position> calculate_ring_positions(const Position& center, f32 inner_radius, f32 outer_radius, i32 num_points);

    /**
     * @brief Normalizes a direction vector
     * @param direction The direction vector to normalize
     * @return Normalized direction vector
     */
    Position normalize_direction(const Position& direction);

    /**
     * @brief Calculates the distance between two positions
     * @param pos1 First position
     * @param pos2 Second position
     * @return Distance between the positions
     */
    f32 calculate_distance(const Position& pos1, const Position& pos2);

    /**
     * @brief Finds the closest position to a target from a list of positions
     * @param positions Vector of positions to search through
     * @param target The target position to find closest to
     * @return Pointer to the closest position, or nullptr if list is empty
     */
    const Position* find_closest_position(const Vec<Position>& positions, const Position& target);

    /**
     * @brief Extends a position towards a target by a specified distance
     * @param from Starting position
     * @param to Target position
     * @param distance Distance to extend
     * @return Extended position
     */
    Position extend_position_towards(const Position& from, const Position& to, f32 distance);

} // namespace zeri
