#pragma once
#include "config.h"
#include "sdk.h"

constexpr f32 PI = 3.1415926535f;  // Define PI constant for use throughout the code

namespace zeri {
    inline static f32 time      = 0.0f;
    inline static f32 next_cast = 0.0f;

    inline static AIHeroClient*  player;
    inline static SpellInstance* zeri_q;
    inline static SpellInstance* zeri_w;
    inline static SpellInstance* zeri_e;
    inline static SpellInstance* zeri_r;

    constexpr f32 CAST_RATE    = 0.25f;
    constexpr f32 E_RANGE      = 300.0f;
    constexpr f32 R_RANGE      = 825.0f;
    constexpr f32 R_SPEED      = 2000.f;
    constexpr f32 Q_BASE_RANGE = 750.0f;

    // Champion theme color constants
    constexpr u8 THEME_RED   = 147;  // Light purple red component
    constexpr u8 THEME_GREEN = 112;  // Light purple green component
    constexpr u8 THEME_BLUE  = 219;  // Light purple blue component
    constexpr u8 THEME_ALPHA = 200;  // Semi-transparent

    // Constants for dynamic auto attack calculation
    constexpr i32 MIN_AUTO_ATTACKS = 3;   // Minimum number of auto attacks to consider
    constexpr i32 MAX_AUTO_ATTACKS = 12;  // Maximum number of auto attacks to consider
    constexpr i32 MAX_LEVEL        = 18;  // Maximum champion level

    inline static bool q_ready() {
        auto spell = player->get_spell(SpellSlot::Q);
        if (!spell) {
            return false;
        }

        return spell->cool_down_remaining() <= (get_latency() / 1000) + 0.5f;
    }

    // Calculates the number of auto attacks to consider based on player level
    // Scales from MIN_AUTO_ATTACKS at level 1 to MAX_AUTO_ATTACKS at level MAX_LEVEL
    inline static i32 get_dynamic_auto_attack_count() {
        i32 current_level = player->level();
        // Clamp level between 1 and MAX_LEVEL
        current_level = max(1, min(current_level, MAX_LEVEL));

        // Calculate scaling factor (0.0 to 1.0)
        f32 level_factor = static_cast<f32>(current_level - 1) / static_cast<f32>(MAX_LEVEL - 1);

        // Linear interpolation between MIN and MAX auto attacks
        f32 attack_count = MIN_AUTO_ATTACKS + (MAX_AUTO_ATTACKS - MIN_AUTO_ATTACKS) * level_factor;

        // Round to nearest integer
        return static_cast<i32>(round(attack_count));
    }

    inline static f32 calc_passive_damage(AIBaseClient* target, bool charged = false) {
        if (!target)  // Basic null checks
        {
            return 0.0f;
        }

        // Ensure sdk.h is included before this function for AIHeroClient definition
        // Clamp player level between 1 and 18 for scaling calculations
        // Cast player to AIHeroClient* to access level(), assuming global player is the hero.
        AIHeroClient* hero_player   = static_cast<AIHeroClient*>(player);
        u32           current_level = hero_player->level();
        if (current_level == 0)
            current_level = 1;  // Treat level 0 as level 1 for safety
        u32 clamped_level = max(1u, min(current_level, 18u));

        f32 ap_value = player->magical_damage();  // Assuming this field holds Ability Power

        if (charged) {
            // Full charge damage calculation: 75-160 (based on level) + 110% AP + 1%-11% (based on level) of target's max health
            f32 base_charged_damage = 75.0f + (85.0f / 17.0f) * (f32) (clamped_level - 1);
            base_charged_damage     = max(75.0f, min(base_charged_damage, 160.0f));

            f32 ap_ratio_charged = 1.10f * ap_value;

            // Calculate percent max health damage (1%-11% based on level)
            f32 percent_max_health_ratio  = 0.01f + (0.10f / 17.0f) * (f32) (clamped_level - 1);
            percent_max_health_ratio      = max(0.01f, min(percent_max_health_ratio, 0.11f));
            f32 percent_max_health_damage = percent_max_health_ratio * target->max_health();

            // Cap percent max health damage at 300 against monsters
            if (target->is_minion()) {
                percent_max_health_damage = min(percent_max_health_damage, 300.0f);
            }

            // Total charged damage = base + AP ratio + percent max health
            return base_charged_damage + ap_ratio_charged + percent_max_health_damage;
        } else {
            // Calculate On-Hit Magic Damage part: 10-25 (based on level) + 3% AP
            f32 base_on_hit_magic_damage = 10.0f + (15.0f / 17.0f) * (f32) (clamped_level - 1);
            // Ensure it doesn't go out of bounds due to potential float inaccuracies
            base_on_hit_magic_damage = max(10.0f, min(base_on_hit_magic_damage, 25.0f));

            f32 ap_ratio_on_hit_damage    = 0.03f * ap_value;
            f32 total_on_hit_magic_damage = base_on_hit_magic_damage + ap_ratio_on_hit_damage;

            // Calculate Execute Health Threshold part: 60-150 (based on level) + 18% AP
            f32 base_execute_health_threshold = 60.0f + (90.0f / 17.0f) * (f32) (clamped_level - 1);
            base_execute_health_threshold     = max(60.0f, min(base_execute_health_threshold, 150.0f));

            f32 ap_ratio_execute_threshold     = 0.18f * ap_value;
            f32 total_execute_health_threshold = base_execute_health_threshold + ap_ratio_execute_threshold;

            // Determine final damage
            if (target->health() < total_execute_health_threshold) {
                // If target is below execute threshold, the damage effectively becomes their current health (it kills them)
                return target->health();
            } else {
                // Otherwise, it's the standard on-hit magic damage from the passive
                return player->calc_magical_damage(target, total_on_hit_magic_damage);
            }
        }
    }

    inline static f32 calc_q_damage(AIBaseClient* target) {
        const auto player  = get_player();
        auto       q_level = player->get_spell(SpellSlot::Q)->level();
        if (q_level == 0)
            return 0.0f;

        // Q base damage and AD ratio per level (1-5)
        constexpr f32 Q_BASE[]     = {15.f, 17.f, 19.f, 21.f, 23.f};
        constexpr f32 Q_AD_RATIO[] = {1.04f, 1.08f, 1.12f, 1.16f, 1.20f};

        // Clamp q_level to valid range (1-5) using sdk.h min/max
        q_level   = max(1, min(5, (int) q_level));
        f32 base  = Q_BASE[q_level - 1];
        f32 ratio = Q_AD_RATIO[q_level - 1];

        // Total AD = base + bonus
        f32 total_ad   = player->base_physical_damage() + player->bonus_physical_damage();
        f32 raw_damage = base + ratio * total_ad;

        return target->calc_physical_damage(player, raw_damage);
    }

    inline static f32 calc_q_damage(AIMinionClient* target_minion) {
        if (!target_minion) {
            return 0.0f;  // Safety check
        }

        u32 q_level = zeri_q->level();
        if (q_level == 0) {
            return 0.0f;
        }

        // Q Damage Calculation arrays (index 0 is unused, levels 1-5 for array access)
        // Base Damage: 15 / 17 / 19 / 21 / 23
        // Total AD Ratio: 104 / 108 / 112 / 116 / 120 %
        // Ensure these values are up-to-date with the current game patch.
        f32 q_base_damage_values[] = {0.0f, 15.0f, 17.0f, 19.0f, 21.0f, 23.0f};
        // Assuming player->physical_damage() returns Total AD, as is common.
        f32 q_total_ad_ratio_values[] = {0.0f, 1.04f, 1.08f, 1.12f, 1.16f, 1.20f};

        f32 raw_q_damage = q_base_damage_values[q_level] + q_total_ad_ratio_values[q_level] * player->physical_damage();

        // Calculate actual damage on the specific minion, considering its armor
        f32 damage_on_minion = target_minion->calc_physical_damage(player, raw_q_damage);
        // log("calc_q_damage: Level %u, Raw: %.2f, OnMinion: %.2f", q_level, raw_q_damage, damage_on_minion);
        return damage_on_minion;
    }

    inline static f32 calc_w_damage(AIBaseClient* target) {
        const auto player  = get_player();
        auto       w_level = player->get_spell(SpellSlot::W)->level();
        if (w_level == 0)
            return 0.0f;

        // W base damage per level (1-5)
        constexpr f32 W_BASE[]   = {30.f, 70.f, 110.f, 150.f, 190.f};
        constexpr f32 W_AD_RATIO = 1.3f;
        constexpr f32 W_AP_RATIO = 0.25f;

        // Clamp w_level to valid range (1-5) using sdk.h min/max
        w_level  = max(1, min(5, (int) w_level));
        f32 base = W_BASE[w_level - 1];

        f32 total_ad   = player->base_physical_damage() + player->bonus_physical_damage();
        f32 ap         = player->magical_damage();
        f32 raw_damage = base + W_AD_RATIO * total_ad + W_AP_RATIO * ap;

        return target->calc_physical_damage(player, raw_damage);
    }

    inline static f32 calc_r_damage(AIBaseClient* target) {
        const auto player  = get_player();
        auto       r_level = player->get_spell(SpellSlot::R)->level();
        if (r_level == 0)
            return 0.0f;

        // R base damage per level (1-3)
        constexpr f32 R_BASE[]         = {175.f, 275.f, 375.f};
        constexpr f32 R_BONUS_AD_RATIO = 0.85f;
        constexpr f32 R_AP_RATIO       = 1.10f;

        // Clamp r_level to valid range (1-3) using sdk.h min/max
        r_level  = max(1, min(3, (int) r_level));
        f32 base = R_BASE[r_level - 1];

        f32 bonus_ad   = player->bonus_physical_damage();
        f32 ap         = player->magical_damage();
        f32 raw_damage = base + R_BONUS_AD_RATIO * bonus_ad + R_AP_RATIO * ap;

        return target->calc_physical_damage(player, raw_damage);
    }

    // Checks if a target is valid (not dead, targetable, visible, etc.) and optionally within a specified range.
    inline static bool is_valid_target(AIBaseClient* target, f32 range = FLT_MAX) {
        if (!target) {
            return false;
        }

        if (target->is_dead() || !target->is_targetable() || !target->is_visible() || target->is_invulnerable() || target->is_zombie()) {
            return false;
        }

        // Check if target is within range
        return get_player()->position().distance(target->position()) <= range;
    }

    // Checks if a given AIMinionClient pointer is valid and represents a standard lane/jungle minion.
    // Excludes pets, plants, wards, and traps from being considered valid minions for certain logic (e.g., farming).
    inline static bool is_valid_minion(AIMinionClient* minion, f32 range = FLT_MAX) {
        if (!minion) {
            return false;
        }

        if (minion->name() == SmallStr("Barrel")) {
            return false;
        }

        if (minion->is_plant() && get_world_cursor().distance(minion->position()) > 200.0f) {
            return false;
        }

        return !minion->is_ally() && !minion->is_trap() && is_valid_target(minion, range);
    }

    // Helper function to count enemies in range
    inline static i32 enemies_in_range(f32 range) {
        i32 count = 0;
        for (auto enemy : get_enemy_heroes()) {
            if (is_valid_target(enemy, range))
                count++;
        }

        return count;
    }

    inline static f32 combo_damage(AIBaseClient* target) {
        // Calculate passive damage
        const f32 passive_damage = calc_passive_damage(target, player->ammo() >= 100.0f);

        // Calculate total number of attacks
        const i32 total_attacks = get_dynamic_auto_attack_count();

        // Calculate Q damage for a single attack
        const f32 q_damage = calc_q_damage(target);
        const f32 r_damage = calc_r_damage(target);

        // Calculate total damage with Q-enhanced attacks
        // We'll use Q damage for the first attack (since Q is typically used first)
        const f32 total_damage = passive_damage + (q_damage * total_attacks) + r_damage;
        return total_damage;
    }

};  // namespace zeri