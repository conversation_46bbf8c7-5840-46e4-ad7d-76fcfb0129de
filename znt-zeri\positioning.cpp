#include "positioning.h"
#include "main.h"
#include "config.h"

namespace zeri {

    // === Constants ===
    constexpr f32 PI = 3.14159265359f;

    // === Position Utility Functions ===

    bool is_pos_under_turret(const Position& pos, f32 turret_range) {
        for (auto turret : get_enemy_turrets()) {
            if (turret->is_dead() || !turret->is_visible() || !turret->is_on_screen())
                continue;

            if (turret->position().distance(pos) < turret_range) {
                return true;
            }
        }

        return false;
    }

    bool get_surrounding_positions(const Position& center_pos, Position& result_pos, bool harass_mode) {
        // Find potential surrounding positions in concentric circles around the target position
        i32 positions_checked      = 0;
        i32 max_positions_to_check = 75;  // Maximum number of positions to evaluate
        i32 circle_segment_size    = 35;  // Size of each segment along the circle
        i32 current_ring_index     = 0;   // Tracks which concentric ring we're examining

        Vec<Position> valid_surrounding_positions = Vec<Position>();

        while (positions_checked < max_positions_to_check && current_ring_index < 10) {
            f32 current_radius   = 50.0f + (current_ring_index * 50.0f);                    // Increase radius for each ring
            i32 points_on_circle = max(8, circle_segment_size - (current_ring_index * 2));  // Fewer points on outer rings

            // Sample points along the current ring
            for (i32 i = 1; i < points_on_circle; i++) {
                positions_checked++;

                // Calculate angle in radians for this point on the circle
                f32 angle_radians = (2 * PI / (points_on_circle - 1)) * i;

                // Calculate the position using parametric circle equation
                Position circle_position = Position(
                    static_cast<f32>(floor(center_pos.x + current_radius * cos(angle_radians))),
                    static_cast<f32>(floor(center_pos.y + current_radius * sin(angle_radians))),
                    center_pos.height
                );

                // Skip positions that are beyond E range
                if (!is_position_reachable_with_e(circle_position))
                    continue;

                // Check if position is safe from enemies
                f32 min_safe_distance = harass_mode ? config->min_harass_safe_distance : config->min_safe_distance;
                if (!is_position_safe_from_enemies(circle_position, min_safe_distance))
                    continue;

                valid_surrounding_positions.push(circle_position);
            }
            current_ring_index++;
        }

        // Sort positions by closest to cursor for optimal movement
        valid_surrounding_positions.sort([](const Position& a, const Position& b) {
            return get_world_cursor().distance(a) > get_world_cursor().distance(b);
        });

        // Return the closest valid position if any were found
        if (valid_surrounding_positions.len() > 0) {
            result_pos = valid_surrounding_positions.first();
            return true;
        }

        return false;
    }

    // === Position Validation Functions ===

    bool is_position_safe_from_enemies(const Position& pos, f32 min_distance) {
        for (auto enemy : get_enemy_heroes()) {
            if (enemy->is_dead() || !enemy->is_visible())
                continue;

            if (pos.distance(enemy->position()) < min_distance) {
                return false;
            }
        }
        return true;
    }

    bool is_position_in_bounds(const Position& pos) {
        // Basic map bounds check for Summoner's Rift
        // These are approximate bounds - adjust if needed
        constexpr f32 MIN_X = -500.0f;
        constexpr f32 MAX_X = 15000.0f;
        constexpr f32 MIN_Y = -500.0f;
        constexpr f32 MAX_Y = 15000.0f;

        return pos.x >= MIN_X && pos.x <= MAX_X && pos.y >= MIN_Y && pos.y <= MAX_Y;
    }

    bool is_position_reachable_with_e(const Position& pos) {
        if (!player)
            return false;

        return player->position().distance(pos) <= E_RANGE;
    }

    bool is_position_reachable(const Position& pos, f32 range) {
        if (!player)
            return false;

        return player->position().distance(pos) <= range;
    }

    bool is_position_safe_for_casting(const Position& pos, f32 ability_range) {
        // Check if position is in bounds
        if (!is_position_in_bounds(pos))
            return false;

        // Check if position is not under enemy turret
        if (is_pos_under_turret(pos))
            return false;

        // Check if position is reachable
        if (!is_position_reachable(pos, ability_range))
            return false;

        return true;
    }

    // === Geometric Helper Functions ===

    Vec<Position> calculate_circle_positions(const Position& center, f32 radius, i32 num_points) {
        Vec<Position> positions;
        
        if (num_points <= 0)
            return positions;

        for (i32 i = 0; i < num_points; ++i) {
            f32 angle = (2.0f * PI * i) / num_points;
            Position pos = Position(
                center.x + radius * cos(angle),
                center.y + radius * sin(angle),
                center.height
            );
            positions.push(pos);
        }

        return positions;
    }

    Vec<Position> calculate_ring_positions(const Position& center, f32 inner_radius, f32 outer_radius, i32 num_points) {
        Vec<Position> positions;
        
        if (num_points <= 0 || inner_radius >= outer_radius)
            return positions;

        // Generate positions between inner and outer radius
        for (i32 i = 0; i < num_points; ++i) {
            f32 angle = (2.0f * PI * i) / num_points;
            f32 radius = inner_radius + (outer_radius - inner_radius) * 0.5f; // Middle of the ring
            
            Position pos = Position(
                center.x + radius * cos(angle),
                center.y + radius * sin(angle),
                center.height
            );
            positions.push(pos);
        }

        return positions;
    }

    Position normalize_direction(const Position& direction) {
        f32 length = sqrt(direction.x * direction.x + direction.y * direction.y);
        
        if (length > 0.001f) {
            return Position(direction.x / length, direction.y / length, direction.height);
        }
        
        return Position(0.0f, 0.0f, direction.height);
    }

    f32 calculate_distance(const Position& pos1, const Position& pos2) {
        return pos1.distance(pos2);
    }

    const Position* find_closest_position(const Vec<Position>& positions, const Position& target) {
        if (positions.len() == 0)
            return nullptr;

        const Position* closest = &positions[0];
        f32 min_distance = target.distance(*closest);

        for (i32 i = 1; i < positions.len(); ++i) {
            f32 distance = target.distance(positions[i]);
            if (distance < min_distance) {
                min_distance = distance;
                closest = &positions[i];
            }
        }

        return closest;
    }

    Position extend_position_towards(const Position& from, const Position& to, f32 distance) {
        return from.extend(to, distance);
    }

} // namespace zeri
